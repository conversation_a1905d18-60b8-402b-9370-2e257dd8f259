defmodule QuoteX.Accounts.Property do
  @moduledoc """
  A property is a physical location that can be quoted.
  """
  use Ash.Resource,
    otp_app: :quotex,
    domain: QuoteX.Accounts,
    data_layer: AshPostgres.DataLayer,
    extensions: [
      AshAdmin.Resource,
      AshPaperTrail.Resource,
      AshArchival.Resource,
      AshOban,
      AshGraphql.Resource,
      AshMoney.Resource
    ]

  graphql do
    type :property

    queries do
      get :get_property, :read
      list :list_properties, :read
    end
  end

  postgres do
    table "properties"
    repo QuoteX.Repo
  end

  paper_trail do
    # default is :uuid
    primary_key_type :uuid_v7
    # default is :snapshot
    change_tracking_mode :changes_only
    # default is false
    store_action_name? true
    # the primary keys are always ignored
    ignore_attributes [:inserted_at, :updated_at]
    # default is []
    ignore_actions [:destroy]
  end

  oban do
    triggers do
      # add a trigger called `:process`
      trigger :process do
        # this trigger calls the `process` action
        action :process
        # for any record that has `processed != true`
        where expr(processed != true)
        # checking for matches every minute
        scheduler_cron("* * * * *")
        on_error(:errored)
        worker_module_name(QuoteX.Accounts.Property.AshOban.Worker.Process)
        scheduler_module_name(QuoteX.Accounts.Property.AshOban.Scheduler.Process)
      end
    end
  end

  actions do
    default_accept [:name, :amount]
    defaults [:read, :destroy, :update, :create]

    update :process do
      change set_attribute(:processed, true)
    end

    update :errored do
      accept []
      argument :error, :struct
      change set_attribute(:processed, false)
    end
  end

  attributes do
    uuid_primary_key :id

    attribute :name, :string do
      allow_nil? false
      constraints min_length: 1
      public? true
    end

    attribute :amount, :money do
      allow_nil? false
      public? true
    end

    attribute :processed, :boolean do
      allow_nil? false
      default false
    end
  end

  identities do
    identity :unique_name, [:name]
  end
end
