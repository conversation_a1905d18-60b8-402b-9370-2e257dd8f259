defmodule QuoteX.Accounts.Reservation do
  use Ash.Resource,
    otp_app: :quotex,
    domain: QuoteX.Accounts,
    extensions: [AshJsonApiWrapper.Resource, AshAdmin.Resource],
    data_layer: AshJsonApiWrapper.DataLayer

  json_api_wrapper do
    base_entity_path "https://openlibrary.org"

    endpoints [
      get("/search.json")
    ]
    fields [:author_name, :title, :type, :id]
    actions [:read]
  end

  attributes do
    uuid_primary_key :id
    attribute :author_name, :string
    attribute :title, :string
    attribute :type, :string
  end

  actions do
    defaults [:read]
  end


end
