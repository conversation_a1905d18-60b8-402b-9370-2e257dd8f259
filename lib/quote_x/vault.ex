defmodule QuoteX.Vault do
  use Cloak.Vault, otp_app: :quotex

  @impl GenServer
  def init(config) do
    config =
      Keyword.put(config, :ciphers, default_ciphers(config[:encryption_key]))

    {:ok, config}
  end

  defp default_ciphers(encryption_key) do
    [
      default: {Cloak.Ciphers.AES.GCM, tag: "AES.GCM.V1", key: decode_env!(encryption_key)},
      retired: []
    ]
  end

  defp decode_env!(nil), do: raise("Missing encryption key")
  defp decode_env!(key) when is_binary(key), do: Base.decode64!(key)
end
