defmodule QuoteXWeb.GraphqlSchema do
  use Absinthe.Schema

  use AshGraphql,
    domains: [QuoteX.Accounts]

  import_types Absinthe.Plug.Types

  # Define the :money scalar type
  scalar :money, description: "A monetary value" do
    parse &parse_money/1
    serialize &serialize_money/1
  end

  # Define the :money_input input object type
  input_object :money_input do
    field :amount, non_null(:float)
    field :currency, non_null(:string)
  end

  defp parse_money(%Absinthe.Blueprint.Input.String{value: value}), do: {:ok, value}
  defp parse_money(%Absinthe.Blueprint.Input.Float{value: value}), do: {:ok, value}
  defp parse_money(_), do: :error

  defp serialize_money(value), do: value

  query do
    # Custom Absinthe queries can be placed here
    @desc """
    Hello! This is a sample query to verify that AshGraphql has been set up correctly.
    Remove me once you have a query of your own!
    """
    field :say_hello, :string do
      resolve fn _, _, _ ->
        {:ok, "Hello from AshGraphql!"}
      end
    end
  end

  mutation do
    # Custom Absinthe mutations can be placed here
  end

  subscription do
    # Custom Absinthe subscriptions can be placed here
  end
end
