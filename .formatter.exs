[
  import_deps: [
    :ash_graphql,
    :absinthe,
    :oban,
    :ash_postgres,
    :ash_admin,
    :ash_phoenix,
    :ash_authentication_phoenix,
    :ash_authentication,
    :ash,
    :reactor,
    :ecto,
    :ecto_sql,
    :phoenix,
    :ash_json_api_wrapper,
    :json_api_wrapper,
    :ash_archival,
    :ash_cloak,
    :ash_paper_trail
  ],
  subdirectories: ["priv/*/migrations"],
  plugins: [Absinthe.Formatter, Spark.Formatter, Phoenix.LiveView.HTMLFormatter],
  inputs: ["*.{heex,ex,exs}", "{config,lib,test}/**/*.{heex,ex,exs}", "priv/*/seeds.exs"]
]
