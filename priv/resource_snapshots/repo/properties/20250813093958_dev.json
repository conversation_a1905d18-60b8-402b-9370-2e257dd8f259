{"attributes": [{"allow_nil?": false, "default": "fragment(\"gen_random_uuid()\")", "generated?": false, "precision": null, "primary_key?": true, "references": null, "scale": null, "size": null, "source": "id", "type": "uuid"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "name", "type": "text"}, {"allow_nil?": false, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "amount", "type": "money_with_currency"}, {"allow_nil?": false, "default": "false", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "processed", "type": "boolean"}, {"allow_nil?": true, "default": "nil", "generated?": false, "precision": null, "primary_key?": false, "references": null, "scale": null, "size": null, "source": "archived_at", "type": "utc_datetime_usec"}], "base_filter": null, "check_constraints": [], "custom_indexes": [], "custom_statements": [], "has_create_action": true, "hash": "ED04E372EC1D24C256430B3A7C10295392CBDB8A38C8B7391B4012597B35C8AE", "identities": [{"all_tenants?": false, "base_filter": null, "index_name": "properties_unique_name_index", "keys": [{"type": "atom", "value": "name"}], "name": "unique_name", "nils_distinct?": true, "where": null}], "multitenancy": {"attribute": null, "global": null, "strategy": null}, "repo": "Elixir.QuoteX.Repo", "schema": null, "table": "properties"}