defmodule QuoteX.Repo.Migrations.InstallAshMoney do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:properties_versions, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :version_action_type, :text, null: false
      add :version_action_name, :text, null: false
      add :version_source_id, :uuid, null: false
      add :changes, :map

      add :version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
    end

    create table(:properties, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
    end

    alter table(:properties_versions) do
      modify :version_source_id,
             references(:properties,
               column: :id,
               name: "properties_versions_version_source_id_fkey",
               type: :uuid,
               prefix: "public"
             )
    end

    alter table(:properties) do
      add :name, :text, null: false
      add :processed, :boolean, null: false, default: false
      add :archived_at, :utc_datetime_usec
    end

    create unique_index(:properties, [:name], name: "properties_unique_name_index")

    create table(:operators, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true
      add :email, :citext, null: false
      add :encrypted_name, :binary, null: false
    end

    create unique_index(:operators, [:email], name: "operators_unique_email_index")
  end

  def down do
    drop_if_exists unique_index(:operators, [:email], name: "operators_unique_email_index")

    drop table(:operators)

    drop_if_exists unique_index(:properties, [:name], name: "properties_unique_name_index")

    alter table(:properties) do
      remove :archived_at
      remove :processed
      remove :name
    end

    drop constraint(:properties_versions, "properties_versions_version_source_id_fkey")

    alter table(:properties_versions) do
      modify :version_source_id, :uuid
    end

    drop table(:properties)

    drop table(:properties_versions)
  end
end
