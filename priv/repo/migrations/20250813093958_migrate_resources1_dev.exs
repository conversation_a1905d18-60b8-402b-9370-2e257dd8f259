defmodule QuoteX.Repo.Migrations.MigrateResources1 do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:properties) do
      add :amount, :money_with_currency, null: false
    end
  end

  def down do
    alter table(:properties) do
      remove :amount
    end
  end
end
